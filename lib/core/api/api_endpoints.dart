import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiEndpoints {
  ApiEndpoints._();

  static String baseUrl = dotenv.get('API_BASE_URL', fallback: '');
  static const String memberLogin = '/member/login/';
  static const String consultantLogin = '/consultant/login/';
  static const String memberCheckIdCard = '/member/check-pid/';
  static const String validateMemberRegister = '/member/register/';
  static const String memberRegister = '/member/register/';

  // OTP endpoints
  static const String otpGenerate = '/otp/generate/';
  static const String otpVerify = '/otp/verify/';

  // Password reset endpoints
  static const String passwordResetRequest = '/password-reset/request/';
  static const String passwordResetVerifyOtp = '/password-reset/verify-otp/';

  // Master data endpoints
  static const String memberTypes = '/mas/member-type';
  static const String governmentSectors = '/mas/government-sector';
  static const String ministriesByGovernmentSector =
      '/mas/ministry/list-by-government-sector';
  static const String departmentsByMinistry =
      '/mas/department/list-by-ministry';
}
