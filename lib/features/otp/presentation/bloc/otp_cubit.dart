import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../domain/usecases/send_otp_usecase.dart';
import '../../domain/usecases/verify_otp_usecase.dart';
import '../../../user/domain/usecases/forgot_password_verify_otp.dart';
import 'otp_state.dart';

class OtpCubit extends Cubit<OtpState> {
  final SendOtpUseCase _sendOtpUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final ForgotPasswordVerifyOtp? _forgotPasswordVerifyOtp;

  // Business logic data moved to cubit
  Timer? _timer;
  String? _currentToken;
  String _email = '';
  String _referenceCode = '';
  String _otpCode = '';
  int _remainingSeconds = 300;
  bool _canResend = false;
  String? _flowType; // 'registration' or 'forgot_password'
  String? _otpToken; // For forgot password flow

  OtpCubit({
    required SendOtpUseCase sendOtpUseCase,
    required VerifyOtpUseCase verifyOtpUseCase,
    ForgotPasswordVerifyOtp? forgotPasswordVerifyOtp,
    String email = '',
    String referenceCode = '',
    String? flowType,
    String? otpToken,
  }) : _sendOtpUseCase = sendOtpUseCase,
       _verifyOtpUseCase = verifyOtpUseCase,
       _forgotPasswordVerifyOtp = forgotPasswordVerifyOtp,
       super(const OtpState.initial()) {
    _email = email;
    _referenceCode = referenceCode;
    _flowType = flowType;
    _otpToken = otpToken;

    // For registration flow, send OTP automatically
    // For forgot password flow, OTP is already sent
    if (email.isNotEmpty && flowType != 'forgot_password') {
      sendOtp(email);
    } else if (flowType == 'forgot_password' && otpToken != null) {
      _currentToken = otpToken;
      emit(OtpState.generated(email: email, referenceCode: referenceCode));
      _startTimer();
    }
  }

  // Getters for accessing business logic data
  String get email => _email;
  String get referenceCode => _referenceCode;
  String get otpCode => _otpCode;
  int get remainingSeconds => _remainingSeconds;
  bool get canResend => _canResend;
  String? get currentToken => _currentToken;

  void _startTimer() {
    _timer?.cancel();
    _remainingSeconds = 300;
    _canResend = false;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        _remainingSeconds--;
        _canResend = _remainingSeconds <= 0;
        emit(
          OtpState.timerUpdated(
            remainingSeconds: _remainingSeconds,
            canResend: _canResend,
          ),
        );
      } else {
        timer.cancel();
        _canResend = true;
        emit(OtpState.timerUpdated(remainingSeconds: 0, canResend: true));
      }
    });
  }

  void updateOtpCode(String code) {
    _otpCode = code;
    // Don't emit state change for OTP code updates to avoid unnecessary rebuilds
    // The UI can access the code via the getter
  }

  Future<void> sendOtp(String email) async {
    _email = email;
    emit(const OtpState.generating());

    final result = await _sendOtpUseCase(SendOtpParams(email: email));

    result.fold(
      (failure) {
        emit(OtpState.error(message: failure.message));
      },
      (response) {
        _currentToken = response.token;
        _email = email;
        _referenceCode = response.refCode;
        emit(OtpState.generated(email: email, referenceCode: response.refCode));
        _startTimer();
      },
    );
  }

  Future<void> verifyOtp(AppLocalizations l10n) async {
    if (_otpCode.length != 6) {
      emit(OtpState.error(message: l10n.otpInvalidCode));
      return;
    }

    if (_currentToken == null) {
      emit(
        const OtpState.error(
          message: 'No OTP token available. Please request a new OTP.',
        ),
      );
      return;
    }

    emit(const OtpState.verifying());

    // Use different verification based on flow type
    if (_flowType == 'forgot_password' && _forgotPasswordVerifyOtp != null) {
      final result = await _forgotPasswordVerifyOtp!(
        ForgotPasswordVerifyOtpParams(
          email: _email,
          otp: _otpCode,
          refCode: _referenceCode,
          otpToken: _currentToken!,
        ),
      );

      result.fold(
        (failure) {
          emit(OtpState.error(message: failure.message));
        },
        (response) {
          _currentToken = response.verifiedToken; // Update token for future use
          emit(const OtpState.verified());
        },
      );
    } else {
      // Registration flow
      final result = await _verifyOtpUseCase(
        VerifyOtpParams(
          token: _currentToken!,
          otp: _otpCode,
          refCode: _referenceCode,
        ),
      );

      result.fold(
        (failure) {
          emit(OtpState.error(message: failure.message));
        },
        (response) {
          _currentToken = response.token; // Update token for future use
          emit(const OtpState.verified());
        },
      );
    }
  }

  Future<void> resendOtp(AppLocalizations l10n) async {
    if (!_canResend) return;

    _otpCode = '';
    emit(const OtpState.resending());

    final result = await _sendOtpUseCase(SendOtpParams(email: _email));

    result.fold(
      (failure) {
        emit(OtpState.error(message: failure.message));
      },
      (response) {
        _currentToken = response.token;
        _referenceCode = response.refCode;
        emit(OtpState.resent(referenceCode: response.refCode));
        _startTimer();
      },
    );
  }

  String? getErrorMessage(AppLocalizations l10n) {
    return state is OtpError ? (state as OtpError).message : null;
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
