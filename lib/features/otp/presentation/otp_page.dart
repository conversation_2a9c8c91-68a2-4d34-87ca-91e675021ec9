import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_warning.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_overlay.dart';
import 'package:get_it/get_it.dart';

import '../domain/usecases/send_otp_usecase.dart';
import '../domain/usecases/verify_otp_usecase.dart';
import '../../user/domain/usecases/forgot_password_verify_otp.dart';
import 'bloc/otp_cubit.dart';
import 'bloc/otp_state.dart';
import 'components/otp_input_field.dart';

@RoutePage()
class OtpPage extends StatefulWidget {
  final String email;
  final String referenceCode;
  final String? flowType; // 'registration' or 'forgot_password'
  final String? otpToken; // For forgot password flow

  const OtpPage({
    super.key,
    required this.email,
    required this.referenceCode,
    this.flowType,
    this.otpToken,
  });

  @override
  State<OtpPage> createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage> {
  late OtpCubit _otpCubit;

  @override
  void initState() {
    super.initState();
    _otpCubit = OtpCubit(
      sendOtpUseCase: GetIt.instance<SendOtpUseCase>(),
      verifyOtpUseCase: GetIt.instance<VerifyOtpUseCase>(),
      forgotPasswordVerifyOtp:
          widget.flowType == 'forgot_password'
              ? GetIt.instance<ForgotPasswordVerifyOtp>()
              : null,
      email: widget.email,
      referenceCode: widget.referenceCode,
      flowType: widget.flowType,
      otpToken: widget.otpToken,
    );
  }

  @override
  void dispose() {
    _otpCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocProvider<OtpCubit>(
      create: (context) => _otpCubit,
      child: Scaffold(
        backgroundColor: AppColors.backgroundDefault,
        appBar: AppBarCommon(title: l10n.otpVerificationTitle),
        body: BlocConsumer<OtpCubit, OtpState>(
          listener: (context, state) {
            switch (state) {
              case OtpInitial():
                break;
              case OtpGenerating():
                break;
              case OtpGenerated():
                break;
              case OtpVerifying():
                break;
              case OtpVerified():
                if (widget.flowType == 'forgot_password') {
                  // For forgot password flow, navigate to reset password page
                  // TODO: Navigate to reset password page with verified token
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(l10n.otpVerificationSuccess),
                      backgroundColor: AppColors.surfaceSuccess,
                    ),
                  );
                  // For now, just go back to login
                  context.router.popUntilRoot();
                } else {
                  // Navigate back to registration page with success result and OTP token
                  context.router.maybePop({
                    'success': true,
                    'moveToStep3': true,
                    'otpToken': _otpCubit.currentToken,
                  });
                }
                break;
              case OtpResending():
                break;
              case OtpResent():
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(l10n.otpResendSuccess),
                    backgroundColor: AppColors.surfaceSuccess,
                  ),
                );
                break;
              case OtpTimerUpdated():
                break;
              case OtpError(message: final message):
                DialogWarning.show(context: context, message: message);
                break;
            }
          },
          builder: (context, state) {
            final isLoading =
                state is OtpGenerating ||
                state is OtpVerifying ||
                state is OtpResending;

            return LoadingOverlay(
              isLoading: isLoading,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    32.h.verticalSpace,
                    Image.asset(
                      'assets/images/otp_cover.png',
                      width: 160.w,
                      height: 160.h,
                      fit: BoxFit.cover,
                    ),
                    24.h.verticalSpace,
                    // Title and subtitle
                    Text(
                      l10n.otpVerificationCodeTitle,
                      style: TextStyle(
                        fontFamily: AppFonts.notoSansThai,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    16.h.verticalSpace,
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyle(
                          fontFamily: AppFonts.notoSansThai,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textDefaultDark,
                          height: 1.5,
                        ),
                        children: [
                          TextSpan(text: '${l10n.otpSentToEmail}\n'),
                          TextSpan(
                            text: _otpCubit.email,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    48.h.verticalSpace,
                    // OTP Input Fields
                    OtpInputField(
                      length: 6,
                      onChanged: (code) {
                        _otpCubit.updateOtpCode(code);
                      },
                      onCompleted: (code) {
                        _otpCubit.updateOtpCode(code);
                      },
                    ),
                    24.h.verticalSpace,
                    // Reference code and timer
                    Text(
                      l10n.otpReferenceCode(
                        _otpCubit.referenceCode,
                        _otpCubit.remainingSeconds,
                      ),
                      style: TextStyle(
                        fontFamily: AppFonts.notoSansThai,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    16.h.verticalSpace,
                    // Resend OTP
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${l10n.otpNotReceived} ',
                          style: TextStyle(
                            fontFamily: AppFonts.notoSansThai,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefaultDark,
                            height: 1.5,
                          ),
                        ),
                        GestureDetector(
                          onTap:
                              _otpCubit.canResend
                                  ? () => _otpCubit.resendOtp(l10n)
                                  : null,
                          child: Text(
                            l10n.otpResendCode,
                            style: TextStyle(
                              fontFamily: AppFonts.notoSansThai,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color:
                                  _otpCubit.canResend
                                      ? AppColors.textPrimary
                                      : AppColors.textSubdude,
                              height: 1.5,
                              decoration:
                                  _otpCubit.canResend
                                      ? TextDecoration.none
                                      : null,
                            ),
                          ),
                        ),
                      ],
                    ),
                    0.2.sh.verticalSpace,
                    // Verify Button
                    PrimaryButton(
                      text: l10n.otpVerifyButton,
                      width: double.infinity,
                      height: 52.h,
                      borderRadius: 50.r,
                      onPressed:
                          _otpCubit.otpCode.length == 6 && !isLoading
                              ? () => _otpCubit.verifyOtp(l10n)
                              : null,
                      isDisabled: _otpCubit.otpCode.length != 6 || isLoading,
                      isLoading: isLoading,
                    ),
                    32.h.verticalSpace,
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
