import 'package:dartz/dartz.dart';
import 'package:mcdc/core/api/network_info.dart';
import 'package:mcdc/core/error/exceptions.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/user/data/datasources/user_api_data_source.dart';
import 'package:mcdc/features/user/data/models/check_id_card_result_model.dart';
import 'package:mcdc/features/user/data/models/login_data_model.dart';
import 'package:mcdc/features/user/data/models/member_register_request_model.dart';
import 'package:mcdc/features/user/data/models/member_register_response_model.dart';
import 'package:mcdc/features/user/data/models/register_validation_error_model.dart';
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/entities/member_register_response.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

class UserRepositoryImpl extends UserRepository {
  final NetworkInfo _networkInfo;
  final UserApiDataSource _userApiDataSource;

  UserRepositoryImpl({
    required NetworkInfo networkInfo,
    required UserApiDataSource userApiDataSource,
  }) : _networkInfo = networkInfo,
       _userApiDataSource = userApiDataSource;

  @override
  Future<Either<Failure, LoginData>> memberLogin({
    required String username,
    required String password,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final result = await _userApiDataSource.memberLogin(
          username: username,
          password: password,
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Unknown server error',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } on Exception catch (e) {
        return Left(UnhandledFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, LoginData>> consultantLogin({
    required String username,
    required String password,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final result = await _userApiDataSource.consultantLogin(
          username: username,
          password: password,
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Unknown server error',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } on Exception catch (e) {
        return Left(UnhandledFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, CheckIdCardResult>> memberCheckIdCard({
    required String idCard,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final result = await _userApiDataSource.memberCheckIdCard(
          idCard: idCard,
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Unknown server error',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } on Exception catch (e) {
        return Left(UnhandledFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, RegisterValidationError>> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final result = await _userApiDataSource.validateMemberRegister(
          username: username,
          email: email,
          phone: phone,
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else if (result.isError && result.data != null) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Unknown server error',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } on Exception catch (e) {
        return Left(UnhandledFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, MemberRegisterResponse>> memberRegister({
    required MemberRegisterRequest request,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final result = await _userApiDataSource.memberRegister(
          request: request.toModel(),
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Unknown server error',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } on Exception catch (e) {
        return Left(UnhandledFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
