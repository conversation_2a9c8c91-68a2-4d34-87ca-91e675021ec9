import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/entities/member_register_response.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';

abstract class UserRepository {
  Future<Either<Failure, LoginData>> memberLogin({
    required String username,
    required String password,
  });

  Future<Either<Failure, LoginData>> consultantLogin({
    required String username,
    required String password,
  });

  Future<Either<Failure, CheckIdCardResult>> memberCheckIdCard({
    required String idCard,
  });

  Future<Either<Failure, RegisterValidationError>> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  });

  Future<Either<Failure, MemberRegisterResponse>> memberRegister({
    required MemberRegisterRequest request,
  });
}
