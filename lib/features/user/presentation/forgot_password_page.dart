import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';

@RoutePage()
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.validateRequired;
    }
    return Validators.validateEmail(value, l10n);
  }

  void _onConfirmPressed() {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Implement forgot password logic
      // For now, just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'ส่งลิงก์รีเซ็ตรหัสผ่านไปยัง ${_emailController.text} แล้ว',
          ),
          backgroundColor: AppColors.textSuccess,
        ),
      );

      // Navigate back to login page
      context.router.maybePop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.forgotPassword),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 32.h),

                // Instruction text
                Text(
                  l10n.forgotPasswordInstruction,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDefaultDark,
                    height: 1.5,
                  ),
                ),

                SizedBox(height: 24.h),

                // Email input field
                CustomTextField(
                  controller: _emailController,
                  label: l10n.emailLabel,
                  keyboardType: TextInputType.emailAddress,
                  isRequired: true,
                  validator: (value) => _validateEmail(value, l10n),
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => _onConfirmPressed(),
                ),

                const Spacer(),

                // Confirm button
                Padding(
                  padding: EdgeInsets.only(bottom: 24.h),
                  child: PrimaryButton(
                    text: l10n.confirm,
                    width: double.infinity,
                    height: 52.h,
                    borderRadius: 50.r,
                    onPressed: _onConfirmPressed,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
