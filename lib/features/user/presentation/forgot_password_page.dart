import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_overlay.dart';
import '../domain/usecases/forgot_password_request.dart';
import 'bloc/forgot_password_cubit.dart';
import 'bloc/forgot_password_state.dart';

@RoutePage()
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  late ForgotPasswordCubit _forgotPasswordCubit;

  @override
  void initState() {
    super.initState();
    _forgotPasswordCubit = ForgotPasswordCubit(
      forgotPasswordRequest: GetIt.instance<ForgotPasswordRequest>(),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _forgotPasswordCubit.close();
    super.dispose();
  }

  String? _validateEmail(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.validateRequired;
    }
    return Validators.validateEmail(value, l10n);
  }

  void _onConfirmPressed() {
    if (_formKey.currentState?.validate() ?? false) {
      final l10n = AppLocalizations.of(context)!;
      _forgotPasswordCubit.requestPasswordReset(_emailController.text, l10n);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocProvider<ForgotPasswordCubit>(
      create: (context) => _forgotPasswordCubit,
      child: Scaffold(
        backgroundColor: AppColors.backgroundDefault,
        appBar: AppBarCommon(title: l10n.forgotPassword),
        body: BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
          listener: (context, state) {
            switch (state) {
              case ForgotPasswordInitial():
                break;
              case ForgotPasswordLoading():
                break;
              case ForgotPasswordSuccess():
                // Navigate to OTP page with forgot password flow
                context.router.push(
                  OtpRoute(
                    email: _emailController.text,
                    referenceCode: state.refCode,
                    flowType: 'forgot_password',
                    otpToken: state.token,
                  ),
                );
                break;
              case ForgotPasswordError():
                // Error is handled in the UI with forceErrorText
                break;
            }
          },
          builder: (context, state) {
            final isLoading = state is ForgotPasswordLoading;

            return LoadingOverlay(
              isLoading: isLoading,
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 32.h),

                        // Instruction text
                        Text(
                          l10n.forgotPasswordInstruction,
                          style: TextStyle(
                            fontFamily: AppFonts.notoSansThai,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textDefaultDark,
                            height: 1.5,
                          ),
                        ),

                        SizedBox(height: 24.h),

                        // Email input field
                        CustomTextField(
                          controller: _emailController,
                          label: l10n.emailLabel,
                          keyboardType: TextInputType.emailAddress,
                          isRequired: true,
                          validator: (value) => _validateEmail(value, l10n),
                          forceErrorText:
                              _forgotPasswordCubit.getEmailErrorMessage(),
                          textInputAction: TextInputAction.done,
                          onSubmitted: (_) => _onConfirmPressed(),
                          onChanged:
                              (value) =>
                                  _forgotPasswordCubit.updateEmail(value),
                        ),

                        const Spacer(),

                        // Confirm button
                        Padding(
                          padding: EdgeInsets.only(bottom: 24.h),
                          child: PrimaryButton(
                            text: l10n.confirm,
                            width: double.infinity,
                            height: 52.h,
                            borderRadius: 50.r,
                            onPressed: isLoading ? null : _onConfirmPressed,
                            isDisabled: isLoading,
                            isLoading: isLoading,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
