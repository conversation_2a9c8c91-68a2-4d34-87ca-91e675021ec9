import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_bloc.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_state.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/language_selection_dialog.dart';
import 'package:mcdc/features/user/presentation/bloc/user_logout_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/user_logout_state.dart';

class ProfileMainPage extends StatefulWidget {
  const ProfileMainPage({super.key});

  @override
  State<ProfileMainPage> createState() => _ProfileMainPageState();
}

class _ProfileMainPageState extends State<ProfileMainPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'ProfileMainPage',
  );

  late UserLogoutCubit _userLogoutCubit;

  @override
  void initState() {
    super.initState();
    _userLogoutCubit = AppInjector.get<UserLogoutCubit>();
  }

  @override
  void dispose() {
    _userLogoutCubit.close();
    super.dispose();
  }

  void _handleLogout() {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              l10n.confirmLogout,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Text(
              l10n.logoutConfirmMessage,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 16.sp,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  l10n.cancel,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    color: AppColors.textSubdude,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Trigger logout using UserLogoutCubit
                  _userLogoutCubit.logout();
                },
                child: Text(
                  l10n.logout,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UserLogoutCubit, UserLogoutState>(
      bloc: _userLogoutCubit,
      listener: (context, state) {
        if (state is Success) {
          // Navigate to welcome page after successful logout
          context.router.replaceAll([const WelcomeRoute()]);
        } else if (state is Error) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Logout failed',
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 14.sp,
                ),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      builder: (context, logoutState) {
        final isLoggingOut = logoutState is Loading;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    _buildProfileHeader(isLoggingOut),
                    Expanded(child: _buildProfileContent()),
                  ],
                ),
                // Show loading overlay during logout
                if (isLoggingOut)
                  Container(
                    color: Colors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primary,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(bool isLoggingOut) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32.r,
            backgroundColor: Colors.black,
            child: Image.asset(
              'assets/images/logo.png',
              width: 50.w,
              height: 50.h,
              errorBuilder: (context, error, stackTrace) {
                return const CircleAvatar(
                  backgroundColor: Colors.red,
                  child: Text(
                    'V',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'บริษัท วีวาสนาดี จำกัด',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDefaultDark,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'ที่ปรึกษาห้างหุ้นส่วนหรือบริษัท',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textDefaultDark,
                  ),
                ),
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isLoggingOut ? null : _handleLogout,
              borderRadius: BorderRadius.circular(8.r),
              child: Padding(
                padding: EdgeInsets.all(8.w),
                child: SvgPicture.asset(
                  'assets/icons/logout.svg',
                  width: 24.w,
                  height: 24.h,
                  colorFilter: ColorFilter.mode(
                    isLoggingOut
                        ? AppColors.textSubdude
                        : AppColors.textPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent() {
    final l10n = AppLocalizations.of(context)!;
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      children: [
        _buildProfileMenuItem(
          icon: Icons.person_outline,
          iconColor: AppColors.textPrimary,
          title: l10n.userAccount,
          onTap: () {
            context.router.push(const MyProfileRoute());
          },
        ),
        _buildProfileMenuItem(
          icon: Icons.account_balance_wallet_outlined,
          iconColor: AppColors.textPrimary,
          title: l10n.payFee,
          onTap: () {
            context.router.pushPath('/pay-fee');
          },
        ),
        SizedBox(height: 12.h),
        Divider(height: 1, color: AppColors.dividerColor),
        SizedBox(height: 12.h),
        _buildProfileMenuItem(
          icon: Icons.help_outline,
          iconColor: AppColors.textPrimary,
          title: l10n.faq,
          onTap: () {
            // Navigate to FAQ screen
            context.router.pushPath('/faq');
          },
        ),
        _buildProfileMenuItem(
          icon: Icons.poll_outlined,
          iconColor: AppColors.textPrimary,
          title: l10n.satisfactionSurvey,
          onTap: () {
            context.router.pushPath('/feedback-form');
          },
        ),
        _buildProfileMenuItem(
          icon: Icons.info_outline,
          iconColor: AppColors.textPrimary,
          title: l10n.aboutUs,
          onTap: () {
            context.router.pushPath('/about-us');
          },
        ),
        SizedBox(height: 12.h),
        _buildLanguageSelector(context),
        SizedBox(height: 12.h),
        _buildProfileMenuItem(
          icon: Icons.security_outlined,
          iconColor: AppColors.textPrimary,
          title: l10n.privacyPolicy,
          onTap: () {
            context.router.pushPath('/user-data-policy');
          },
        ),
        _buildProfileMenuItem(
          icon: Icons.security_outlined,
          iconColor: AppColors.textPrimary,
          title: l10n.appPolicy,
          onTap: () {
            context.router.pushPath('/application-policy');
          },
        ),
        SizedBox(height: 12.h),
        Divider(height: 1, color: AppColors.dividerColor),
        SizedBox(height: 24.h),
      ],
    );
  }

  Widget _buildProfileMenuItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, size: 24.w, color: iconColor),
      title: Text(
        title,
        style: TextStyle(
          fontFamily: AppFonts.notoSansThai,
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
          color: AppColors.textDefaultDark,
        ),
      ),
      onTap: onTap,
    );
  }

  Widget _buildLanguageSelector(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, languageState) {
        final isThaiSelected = languageState.locale.languageCode == 'th';

        return InkWell(
          onTap: () => showLanguageSelectionDialog(context),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Row(
              children: [
                Icon(Icons.language, size: 24.w, color: AppColors.textPrimary),
                SizedBox(width: 16.w),
                Text(
                  l10n.changeLanguage,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textDefaultDark,
                  ),
                ),
                const Spacer(),
                Text(
                  l10n.thai,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color:
                        isThaiSelected
                            ? AppColors.textDefaultDark
                            : AppColors.textSubdude,
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  height: 16.h,
                  width: 1,
                  color: AppColors.dividerColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  l10n.english,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color:
                        isThaiSelected
                            ? AppColors.textSubdude
                            : AppColors.textDefaultDark,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
